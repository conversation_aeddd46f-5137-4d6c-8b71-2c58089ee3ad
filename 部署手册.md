# YYHIS-Server 部署手册

## 项目概述
- **项目名称**: yyhis-server
- **技术栈**: Spring Boot 3.5.3 + MySQL + MyBatis-Plus
- **Java版本**: JDK 21
- **默认端口**: 6596

## 环境要求

### 服务器环境
- **操作系统**: Linux (推荐 CentOS 7+ 或 Ubuntu 18.04+)
- **Java**: JDK 21 或更高版本
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘空间**: 最低 10GB

### 数据库环境
- **MySQL**: 5.7+ 或 8.0+


## 部署步骤

### 1. 环境准备

#### 安装 JDK 21
```bash
# CentOS/RHEL
sudo yum install java-21-openjdk java-21-openjdk-devel

# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-21-jdk

# 验证安装
java -version
```

#### 配置环境变量
```bash
# 编辑 /etc/profile 或 ~/.bashrc
export JAVA_HOME=/usr/lib/jvm/java-21-openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 使配置生效
source /etc/profile
```

### 2. 数据库准备

#### MySQL 配置
```sql
-- 导入sql

```

### 3. 应用部署

#### 方式一：使用预编译的 JAR 包
```bash
# 1. 创建应用目录
sudo mkdir -p /opt/yyhis-server
cd /opt/yyhis-server

# 2. 上传 JAR 包
# 将 target/yyhis-0.0.1-SNAPSHOT.jar 上传到服务器

# 3. 创建配置文件目录
mkdir -p config

# 4. 上传配置文件到 config 目录

# 5. 启动应用
java -jar yyhis-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```


