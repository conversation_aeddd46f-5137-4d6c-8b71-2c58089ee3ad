package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B023Converter;
import cn.wbw.yyhis.model.dto.B023UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.mapper.B023Mapper;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * <p>
 * B023-患者诊断信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@RequiredArgsConstructor
public class B023ServiceImpl extends ServiceImpl<B023Mapper, B023> implements B023Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B023Converter converter = B023Converter.INSTANCE;

    @Override
    public B023 getByCompositeId(String diagId, String diagSource) {
        return getOne(buildCompositeIdWrapper(diagId, diagSource));
    }

    @Override
    public boolean removeByCompositeId(String diagId, String diagSource) {
        B023 existingB023 = this.getByCompositeId(diagId, diagSource);
        if (existingB023 == null) {
            return true;
        }
        remove(buildCompositeIdWrapper(diagId, diagSource));
        integrationClientService.deleteHospitalDataByCompositeKey(existingB023.getVisitSn(), B023.TABLE_NAME, existingB023);
        return true;
    }

    @Override
    public boolean updateByCompositeId(B023 b023) {
        if (b023 == null || !StringUtils.hasText(b023.getDiagId()) || !StringUtils.hasText(b023.getDiagSource())) {
            return false;
        }
        return update(b023, buildCompositeIdWrapper(b023.getDiagId(), b023.getDiagSource()));
    }

    @Override
    public B023 addPatientDiagnosis(B023UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B023 b023 = new B023();

        // 2.1 从 B032 填充公共信息
        b023.setVisitSn(b032.getVisitSn());
        b023.setPatientId(b032.getPatientId());
        b023.setName(b032.getName());
        b023.setInpatientNo(b032.getInpatientNo());
        b023.setHospitalCode(b032.getHospitalCode());
        b023.setHospitalName(b032.getHospitalName());
        b023.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充诊断特有信息
        b023.setDiagName(dto.getDiagName());
        b023.setDiagCode(dto.getDiagCode());
        b023.setDiagType(dto.getDiagType());
        b023.setMaindiagMark(dto.getMaindiagMark());
        b023.setDiagDatetime(dto.getDiagDatetime());
        b023.setDiagExplanation(dto.getDiagExplanation());

        // 2.3 生成主键和其他必要字段
        b023.setDiagId(IdUtil.fastSimpleUUID());
        b023.setDiagSource("HIS");
        b023.setDiagSerialNumber("1");
        b023.setRecordStatus(1);
        b023.setVisitType("1");
        b023.setYyRecordMd5(IdUtil.fastSimpleUUID());


        b023.setRecordDatetime(LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b023.setRecordUpdateDatetime(LocalDateTime.now());

        // 3. 保存到数据库
        this.save(b023);
        integrationClientService.pushHospitalDataAsync(b023.getVisitSn(), B023.TABLE_NAME, b023);
        return b023;
    }


    @Override
    public boolean updatePatientDiagnosis(B023UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getDiagId()) || !StringUtils.hasText(dto.getDiagSource())) {
            return false;
        }
        // 我们只更新DTO中提供的字段，所以先根据ID获取现有实体
        B023 existingB023 = this.getByCompositeId(dto.getDiagId(), dto.getDiagSource());
        if (existingB023 == null) {
            throw new IllegalArgumentException("未找到要更新的诊断记录");
        }

        // 使用 converter 将DTO中的非空值更新到实体中
        B023 b023ToUpdate = converter.dtoToEntity(dto);
        b023ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());


        // mybatis plus update会忽略null值，所以直接用转换后的对象更新即可
        this.updateByCompositeId(b023ToUpdate);
        integrationClientService.pushHospitalDataAsync(b023ToUpdate.getVisitSn(), B023.TABLE_NAME, b023ToUpdate);
        return true;
    }

    private QueryWrapper<B023> buildCompositeIdWrapper(String diagId, String diagSource) {
        QueryWrapper<B023> wrapper = new QueryWrapper<>();
        wrapper.eq("diag_id", diagId);
        wrapper.eq("diag_source", diagSource);
        return wrapper;
    }
}