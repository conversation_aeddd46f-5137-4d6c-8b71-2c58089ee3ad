package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.TnmTStagingConverter;
import cn.wbw.yyhis.mapper.TnmTStagingMapper;
import cn.wbw.yyhis.model.dto.TnmTStagingUpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.TnmTStaging;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.TnmTStagingService;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * TNM分期记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Service
@RequiredArgsConstructor
public class TnmTStagingServiceImpl extends ServiceImpl<TnmTStagingMapper, TnmTStaging> implements TnmTStagingService {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final TnmTStagingConverter converter = TnmTStagingConverter.INSTANCE;

    @Override
    public TnmTStaging addTnmTStagingRecord(TnmTStagingUpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }
        TnmTStaging existingRecord = this.lambdaQuery().eq(TnmTStaging::getVisitSn, b032.getVisitSn()).one();
        if (existingRecord != null) {

            TnmTStaging recordToUpdate = converter.dtoToEntity(dto);
            recordToUpdate.setRecordUpdateDatetime(LocalDateTime.now());
            this.updateById(recordToUpdate);

        } else {

            // 2. 数据转换和填充
            TnmTStaging tnmTStaging = new TnmTStaging();

            // 2.1 从 B032 填充公共信息
            tnmTStaging.setVisitSn(b032.getVisitSn());

            // 2.2 从 DTO 填充TNM分期记录特有信息
            tnmTStaging.setDiagnosisItemCode(dto.getDiagnosisItemCode());
            tnmTStaging.setTnmTStaging(dto.getTnmTStaging());
            tnmTStaging.setTnmNStaging(dto.getTnmNStaging());
            tnmTStaging.setTnmMStaging(dto.getTnmMStaging());
            tnmTStaging.setFigoStagingCervicalCancer(dto.getFigoStagingCervicalCancer());
            tnmTStaging.setCnlcStagingHepatocellular(dto.getCnlcStagingHepatocellular());
            tnmTStaging.setAnnArborStagingLymphoma(dto.getAnnArborStagingLymphoma());
            tnmTStaging.setStagingDatetime(dto.getStagingDatetime());

            // 2.3 生成主键和时间戳
            tnmTStaging.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
            tnmTStaging.setRecordDatetime(LocalDateTime.now());
            tnmTStaging.setRecordUpdateDatetime(LocalDateTime.now());

            // 3. 保存到数据库
            this.save(tnmTStaging);
        }

        TnmTStaging tnmTStaging = this.lambdaQuery().eq(TnmTStaging::getVisitSn, b032.getVisitSn()).one();
        B023 b023 = genData( tnmTStaging);
        integrationClientService.pushHospitalDataAsync(b023.getVisitSn(), B023.TABLE_NAME, b023);
        return tnmTStaging;
    }

    private  B023 genData(TnmTStaging tnmTStaging) {
        String visitSn = tnmTStaging.getVisitSn();
        B032 b032 = b032Service.getById(visitSn);
        B023 b023 = new B023();
        b023.setVisitSn(b032.getVisitSn());
        b023.setPatientId(b032.getPatientId());
        b023.setName(b032.getName());
        b023.setInpatientNo(b032.getInpatientNo());
        b023.setHospitalCode(b032.getHospitalCode());
        b023.setHospitalName(b032.getHospitalName());
        b023.setMedicalRecordNo(b032.getMedicalRecordNo());

        b023.setDiagId(tnmTStaging.getRecordSn());

        b023.setDiagSerialNumber("1");
        b023.setRecordStatus(1);
        b023.setVisitType("1");
        b023.setYyRecordMd5(tnmTStaging.getRecordSn());

        // 诊断类型 tnm分期类型
        b023.setDiagType("tnm分期诊断");
        // 诊断状态 确认诊断
        b023.setDiagStatus("确认诊断");
        // 诊断编码 tnm分期
        String tnm = tnmTStaging.getTnmMStaging() + tnmTStaging.getTnmNStaging() + tnmTStaging.getTnmTStaging();
        b023.setDiagCode(tnm);
        // 诊断名称 临床诊断+tnm分期
        b023.setDiagName(tnmTStaging.getDiagnosisItemCode() + tnm);
        // 诊断时间  分期时间
        LocalDateTime stagingDatetime = tnmTStaging.getStagingDatetime();
        if (stagingDatetime != null) {
            b023.setDiagDatetime(stagingDatetime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        // 是否明确诊断
        b023.setConfirmedDiagMark("是");
        // 是否主要诊断
        b023.setMaindiagMark("否");
        // 诊断数据来源 HIS
        b023.setDiagSource("HIS");
        // 上报状态
        b023.setYyUploadStatus(0);
        // 抽取完成时间
        b023.setYyEtlTime(LocalDateTime.now());

        b023.setRecordDatetime(LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b023.setRecordUpdateDatetime(LocalDateTime.now());
        return b023;
    }



    @Override
    public Boolean deleteByVisitSn(String visitSn) {
        TnmTStaging tnmTStaging = this.lambdaQuery().eq(TnmTStaging::getVisitSn, visitSn).one();
        this.lambdaUpdate().eq(TnmTStaging::getVisitSn, visitSn).remove();
        B023 b023 = genData(tnmTStaging);
        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B023.TABLE_NAME, b023);
        return true;
    }

}
