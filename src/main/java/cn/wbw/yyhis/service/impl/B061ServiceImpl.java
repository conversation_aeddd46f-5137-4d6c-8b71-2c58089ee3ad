package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B061Converter;
import cn.wbw.yyhis.mapper.B061Mapper;
import cn.wbw.yyhis.model.dto.B061UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B051;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B061Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class B061ServiceImpl extends ServiceImpl<B061Mapper, B061> implements B061Service {

    private final B061Converter converter = B061Converter.INSTANCE;
    private final IntegrationClientService integrationClientService;
    private final B032Service b032Service;

    @Override
    public long countByVisitSn(String visitSn) {
        return this.baseMapper.selectCount(new QueryWrapper<B061>().eq("visit_sn", visitSn));
    }

    @Override
    public B061 addAdmissionRecord(B061UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 数据转换和填充
        B061 b061 = converter.toEntity(dto);

        // 2.1 从 B032 填充公共信息
        b061.setVisitSn(b032.getVisitSn());
        b061.setPatientId(b032.getPatientId());
        b061.setInpatientNo(b032.getInpatientNo());
        b061.setHospitalCode(b032.getHospitalCode());
        b061.setHospitalName(b032.getHospitalName());
        b061.setMedicalRecordNo(b032.getMedicalRecordNo());

        // 生成业务主键和设置时间
        b061.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b061.setRecordDatetime(LocalDateTime.now());
        b061.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b061.setRecordStatus(1);
        b061.setRecordTitle("日常病程记录");
        b061.setRecordType("");
        b061.setYyRecordMd5(IdUtil.fastSimpleUUID());

        // 保存到数据库
        this.save(b061);

        integrationClientService.pushHospitalDataAsync(b061.getVisitSn(), B061.TABLE_NAME, b061);
        return b061;
    }

    @Override
    public boolean updateAdmissionRecord(B061UpsertDTO dto) {
        if (dto == null || dto.getRecordSn() == null) {
            return false;
        }

        B061 existingB061 = this.getById(dto.getRecordSn());
        if (existingB061 == null) {
            throw new IllegalArgumentException("未找到要更新的日常病程记录");
        }

        B061 b061ToUpdate = converter.toEntity(dto);
        b061ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        integrationClientService.pushHospitalDataAsync(existingB061.getVisitSn(), B061.TABLE_NAME, existingB061);

        return this.updateById(b061ToUpdate);
    }

    @Override
    public Boolean deleteByRecordSn(String recordSn) {
        B061 one = this.lambdaQuery().eq(B061::getRecordSn, recordSn).one();
        if (one == null) {
            throw new IllegalArgumentException("未找到要删除的日常病程记录");
        }
        this.lambdaUpdate().eq(B061::getRecordSn, recordSn).remove();
        integrationClientService.deleteHospitalDataByCompositeKey(one.getVisitSn(), B061.TABLE_NAME, one);
        return true;

    }
}