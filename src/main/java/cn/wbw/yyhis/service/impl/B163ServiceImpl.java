package cn.wbw.yyhis.service.impl;

import cn.wbw.yyhis.converter.B163Converter;
import cn.wbw.yyhis.model.dto.B163DTO;
import cn.wbw.yyhis.model.entity.B163;
import cn.wbw.yyhis.mapper.B163Mapper;
import cn.wbw.yyhis.service.B163Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 分子病理检测记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Service
public class B163ServiceImpl extends ServiceImpl<B163Mapper, B163> implements B163Service {
    @Autowired
    private B163Converter b163Converter;
    @Autowired
    private IntegrationClientService integrationClientService;

    @Override
    public Boolean updateDto(B163DTO b163DTO) {
        // 我们只更新DTO中提供的字段，所以先根据ID获取现有实体
        B163 existingB163 = this.getById(b163DTO.getReportNo());
        if (existingB163 == null) {
            throw new IllegalArgumentException("未找到要更新的分子病理检查记录");
        }

        // 使用 converter 将DTO中的非空值更新到实体中
        B163 b163 = b163Converter.dtoToEntity(b163DTO);
        b163.setRecordUpdateDatetime(LocalDateTime.now());
        integrationClientService.pushHospitalDataAsync(existingB163.getVisitSn(), B163.TABLE_NAME, b163);
        return true;
    }

    @Override
    public Boolean deleteByReportNo(String id) {
        B163 one = this.getById(id);
        if (one == null) {
            return true;
        }
        this.removeById(id);
        integrationClientService.deleteHospitalDataByCompositeKey(one.getVisitSn(), B163.TABLE_NAME, one);
        return true;
    }

} 