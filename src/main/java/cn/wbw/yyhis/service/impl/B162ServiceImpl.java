package cn.wbw.yyhis.service.impl;

import cn.wbw.yyhis.converter.B162Converter;
import cn.wbw.yyhis.model.dto.B162DTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B162;
import cn.wbw.yyhis.mapper.B162Mapper;
import cn.wbw.yyhis.service.B162Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 病理检查记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Service
public class B162ServiceImpl extends ServiceImpl<B162Mapper, B162> implements B162Service {
    @Autowired
    private B162Converter b162Converter;
    @Autowired
    private IntegrationClientService integrationClientService;

    @Override
    public Boolean updateDto(B162DTO b162DTO) {
        // 我们只更新DTO中提供的字段，所以先根据ID获取现有实体
        B162 existingB162 = this.getById(b162DTO.getReportNo());
        if (existingB162 == null) {
            throw new IllegalArgumentException("未找到要更新的病理检查记录");
        }

        // 使用 converter 将DTO中的非空值更新到实体中
        B162 b162 = b162Converter.dtoToEntity(b162DTO);
        b162.setRecordUpdateDatetime(LocalDateTime.now());
        integrationClientService.pushHospitalDataAsync(existingB162.getVisitSn(), B162.TABLE_NAME, b162);
        return true;
    }

    @Override
    public Boolean deleteByReportNo(String id) {
        B162 byId = this.getById(id);
        if (byId == null) {
            return true;
        }
        this.removeById(id);
        integrationClientService.deleteHospitalDataByCompositeKey(byId.getVisitSn(), B162.TABLE_NAME, byId);
        return true;

    }

}