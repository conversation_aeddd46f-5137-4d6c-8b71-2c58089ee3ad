package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B0615Converter;
import cn.wbw.yyhis.mapper.B0615Mapper;
import cn.wbw.yyhis.model.dto.B0615UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.model.entity.B0615;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B0615Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class B0615ServiceImpl extends ServiceImpl<B0615Mapper, B0615> implements B0615Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B0615Converter converter = B0615Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B0615 addProcedureRecord(B0615UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B0615 b0615 = new B0615();

        // 2.1 从 B032 填充公共信息
        b0615.setVisitSn(b032.getVisitSn());
        b0615.setPatientId(b032.getPatientId());
        b0615.setInpatientNo(b032.getInpatientNo());
        b0615.setHospitalCode(b032.getHospitalCode());
        b0615.setHospitalName(b032.getHospitalName());
        b0615.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充有创操作特有信息
        b0615.setPerfromDatetime(dto.getPerfromDatetime());
        b0615.setProcedureName(dto.getProcedureName());
        b0615.setRecordContent(dto.getRecordContent());
        b0615.setMattersNeedingAttention(dto.getMattersNeedingAttention());

        // 2.3 生成主键
        b0615.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b0615.setRecordDatetime(LocalDateTime.now());
        b0615.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b0615.setMedicalNoteDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b0615.setRecordStatus(1);
        b0615.setRecordText("");
        b0615.setRecordTitle("有创诊疗操作记录");
        b0615.setYyRecordMd5(IdUtil.fastSimpleUUID());

        // 3. 保存到数据库
        this.save(b0615);

        // 4. 重新从数据库查询最新数据用于推送
        B0615 latestB0615 = this.getById(b0615.getRecordSn());
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0615.getHospitalCode());
        b061.setVisitSn(latestB0615.getVisitSn());
        b061.setRecordTitle(latestB0615.getRecordTitle());
        b061.setRecordType("有创诊疗操作记录");
        b061.setRecordDatetime(latestB0615.getRecordDatetime());
        // 以字段英文名与字段内容由冒号拼接，再由~!@#四个特殊英文字符，如下：
        // medical_note_date:字段内容~!@#perfrom_datetime:字段内容~!@#procedure_name:字段内容~!@#record_content:字段内容~!@#matters_needing_attention:字段内容
        String recordContent = buildRecordContent(latestB0615);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0615.getVisitSn(), B061.TABLE_NAME, b061);

        return b0615;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProcedureRecord(B0615UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getRecordSn())) {
            return false;
        }

        B0615 existingB0615 = this.getById(dto.getRecordSn());
        if (existingB0615 == null) {
            throw new IllegalArgumentException("未找到要更新的有创操作记录");
        }

        B0615 b0615ToUpdate = converter.toEntity(dto);
        b0615ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        boolean updated = this.updateById(b0615ToUpdate);

        // 推送更新后的数据到第三方系统
        if (updated) {
            // 重新从数据库查询最新数据用于推送
            B0615 latestB0615 = this.getById(b0615ToUpdate.getRecordSn());
            B061 b061 = new B061();
            b061.setHospitalCode(latestB0615.getHospitalCode());
            b061.setVisitSn(latestB0615.getVisitSn());
            b061.setRecordTitle(latestB0615.getRecordTitle());
            b061.setRecordType("有创诊疗操作记录");
            b061.setRecordDatetime(latestB0615.getRecordDatetime());

            // 按照注释中的格式拼接字段内容
            String recordContent = buildRecordContent(latestB0615);
            b061.setRecordContent(recordContent);

            integrationClientService.pushHospitalDataAsync(latestB0615.getVisitSn(), B061.TABLE_NAME, b061);
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B0615 b0615 = this.lambdaQuery().eq(B0615::getVisitSn, visitSn).one();
        if (b0615 == null) {
            throw new IllegalArgumentException("未找到要删除的有创操作记录");
        }
        this.lambdaUpdate().eq(B0615::getVisitSn, visitSn).remove();
        B061 b061 = new B061();
        b061.setHospitalCode(b0615.getHospitalCode());
        b061.setVisitSn(b0615.getVisitSn());
        b061.setRecordTitle(b0615.getRecordTitle());
        b061.setRecordType("有创诊疗操作记录");
        b061.setRecordDatetime(b0615.getRecordDatetime());

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B061.TABLE_NAME, b061);
        return true;
    }

    /**
     * 构建记录内容字符串
     * 按照指定格式拼接字段：字段英文名:字段内容~!@#字段英文名:字段内容...
     *
     * @param b0615 有创诊疗操作记录实体
     * @return 拼接后的记录内容字符串
     */
    private String buildRecordContent(B0615 b0615) {
        StringBuilder recordContentBuilder = new StringBuilder();

        // 按照注释中的顺序拼接字段
        recordContentBuilder.append("medical_note_date:").append(b0615.getMedicalNoteDate() != null ? b0615.getMedicalNoteDate() : "").append("~!@#");
        recordContentBuilder.append("perfrom_datetime:").append(b0615.getPerfromDatetime() != null ? b0615.getPerfromDatetime() : "").append("~!@#");
        recordContentBuilder.append("procedure_name:").append(b0615.getProcedureName() != null ? b0615.getProcedureName() : "").append("~!@#");
        recordContentBuilder.append("record_content:").append(b0615.getRecordContent() != null ? b0615.getRecordContent() : "").append("~!@#");
        recordContentBuilder.append("matters_needing_attention:").append(b0615.getMattersNeedingAttention() != null ? b0615.getMattersNeedingAttention() : "");

        return recordContentBuilder.toString();
    }
}