package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B0611Converter;
import cn.wbw.yyhis.model.dto.B0611UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.model.entity.B0611;
import cn.wbw.yyhis.mapper.B0611Mapper;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B0611Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * <p>
 * 首次病程记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@RequiredArgsConstructor
public class B0611ServiceImpl extends ServiceImpl<B0611Mapper, B0611> implements B0611Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;

    private final B0611Converter converter = B0611Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B0611 addFirstCourseRecord(B0611UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B0611 b0611 = new B0611();

        // 2.1 从 B032 填充公共信息
        b0611.setVisitSn(b032.getVisitSn());
        b0611.setPatientId(b032.getPatientId());
        b0611.setInpatientNo(b032.getInpatientNo());
        b0611.setHospitalCode(b032.getHospitalCode());
        b0611.setHospitalName(b032.getHospitalName());
        b0611.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充首次病程记录特有信息
        b0611.setPrimaryDiagnosis(dto.getPrimaryDiagnosis());
        b0611.setCaseCharacter(dto.getCaseCharacter());
        b0611.setChiefComplaint(dto.getChiefComplaint());
        b0611.setPhysicalExam(dto.getPhysicalExam());
        b0611.setAuxiliaryExam(dto.getAuxiliaryExam());
        b0611.setDiagnosisBasis(dto.getDiagnosisBasis());
        b0611.setDifferentiatedDiagnosisDesc(dto.getDifferentiatedDiagnosisDesc());
        b0611.setTreatmentPlan(dto.getTreatmentPlan());

        // 2.3 生成主键
        b0611.setRecordSn(UUID.randomUUID().toString().replace("-", ""));

        b0611.setRecordDatetime(LocalDateTime.now());
        b0611.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b0611.setDiagnosisDifferentiatedDiagnosisDesc("");
        b0611.setMedicalNoteDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b0611.setRecordStatus(1);
        b0611.setRecordText("");
        b0611.setRecordTitle("首次病程记录");
        b0611.setYyRecordMd5(IdUtil.fastSimpleUUID());

        // 3. 保存到数据库
        this.save(b0611);

        // 重新从数据库查询最新数据用于推送
        B0611 latestB0611 = this.getById(b0611.getRecordSn());
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0611.getHospitalCode());
        b061.setVisitSn(latestB0611.getVisitSn());
        b061.setRecordTitle(latestB0611.getRecordTitle());
        b061.setRecordType("首次病程记录");
        b061.setRecordDatetime(latestB0611.getRecordDatetime());
        // 以字段英文名与字段内容由冒号拼接，再由~!@#四个特殊英文字符，如下：
        // medical_note_date:字段内容~!@#chief_complaint:字段内容~!@#case_character:字段内容~!@#physical_exam:字段内容~!@#auxiliary_exam:字段内容~!@#primary_diagnosis:字段内容~!@#diagnosis_basis:字段内容~!@#differentiated_diagnosis_desc:字段内容~!@#diagnosis_differentiated_diagnosis_desc:字段内容~!@#treatment_plan:字段内容
        String recordContent = buildRecordContent(latestB0611);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0611.getVisitSn(), B0611.TABLE_NAME, b061);
        return b0611;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFirstCourseRecord(B0611UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getRecordSn())) {
            return false;
        }

        B0611 existingB0611 = this.getById(dto.getRecordSn());
        if (existingB0611 == null) {
            throw new IllegalArgumentException("未找到要更新的首次病程记录");
        }

        B0611 b0611ToUpdate = converter.dtoToEntity(dto);
        b0611ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        this.updateById(b0611ToUpdate);

        // 重新从数据库查询最新数据用于推送
        B0611 latestB0611 = this.lambdaQuery().eq(B0611::getVisitSn, dto.getVisitSn()).one();
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0611.getHospitalCode());
        b061.setVisitSn(latestB0611.getVisitSn());
        b061.setRecordTitle(latestB0611.getRecordTitle());
        b061.setRecordType("首次病程记录");
        b061.setRecordDatetime(latestB0611.getRecordDatetime());

        // 按照注释中的格式拼接字段内容
        String recordContent = buildRecordContent(latestB0611);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0611.getVisitSn(), B061.TABLE_NAME, b061);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B0611 b0611 = this.lambdaQuery().eq(B0611::getVisitSn, visitSn).one();
        if (b0611 == null) {
            throw new IllegalArgumentException("未找到要删除的首次病程记录");
        }
        this.lambdaUpdate().eq(B0611::getVisitSn, visitSn).remove();
        B061 b061 = new B061();
        b061.setHospitalCode(b0611.getHospitalCode());
        b061.setVisitSn(b0611.getVisitSn());
        b061.setRecordTitle(b0611.getRecordTitle());
        b061.setRecordType("首次病程记录");
        b061.setRecordDatetime(b0611.getRecordDatetime());

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B061.TABLE_NAME, b061);
        return true;
    }

    /**
     * 构建记录内容字符串
     * 按照指定格式拼接字段：字段英文名:字段内容~!@#字段英文名:字段内容...
     *
     * @param b0611 首次病程记录实体
     * @return 拼接后的记录内容字符串
     */
    private String buildRecordContent(B0611 b0611) {
        StringBuilder recordContentBuilder = new StringBuilder();

        // 按照注释中的顺序拼接字段
        recordContentBuilder.append("medical_note_date:").append(b0611.getMedicalNoteDate() != null ? b0611.getMedicalNoteDate() : "").append("~!@#");
        recordContentBuilder.append("chief_complaint:").append(b0611.getChiefComplaint() != null ? b0611.getChiefComplaint() : "").append("~!@#");
        recordContentBuilder.append("case_character:").append(b0611.getCaseCharacter() != null ? b0611.getCaseCharacter() : "").append("~!@#");
        recordContentBuilder.append("physical_exam:").append(b0611.getPhysicalExam() != null ? b0611.getPhysicalExam() : "").append("~!@#");
        recordContentBuilder.append("auxiliary_exam:").append(b0611.getAuxiliaryExam() != null ? b0611.getAuxiliaryExam() : "").append("~!@#");
        recordContentBuilder.append("primary_diagnosis:").append(b0611.getPrimaryDiagnosis() != null ? b0611.getPrimaryDiagnosis() : "").append("~!@#");
        recordContentBuilder.append("diagnosis_basis:").append(b0611.getDiagnosisBasis() != null ? b0611.getDiagnosisBasis() : "").append("~!@#");
        recordContentBuilder.append("differentiated_diagnosis_desc:").append(b0611.getDifferentiatedDiagnosisDesc() != null ? b0611.getDifferentiatedDiagnosisDesc() : "").append("~!@#");
        recordContentBuilder.append("diagnosis_differentiated_diagnosis_desc:").append(b0611.getDiagnosisDifferentiatedDiagnosisDesc() != null ? b0611.getDiagnosisDifferentiatedDiagnosisDesc() : "").append("~!@#");
        recordContentBuilder.append("treatment_plan:").append(b0611.getTreatmentPlan() != null ? b0611.getTreatmentPlan() : "");

        return recordContentBuilder.toString();
    }
}