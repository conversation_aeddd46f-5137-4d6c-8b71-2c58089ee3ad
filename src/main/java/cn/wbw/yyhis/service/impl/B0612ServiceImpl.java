package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B0612Converter;
import cn.wbw.yyhis.model.dto.B0612UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.model.entity.B0612;
import cn.wbw.yyhis.mapper.B0612Mapper;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B0612Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * <p>
 * 会诊记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@RequiredArgsConstructor
public class B0612ServiceImpl extends ServiceImpl<B0612Mapper, B0612> implements B0612Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B0612Converter converter = B0612Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B0612 addConsultationRecord(B0612UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B0612 b0612 = new B0612();

        // 2.1 从 B032 填充公共信息
        b0612.setVisitSn(b032.getVisitSn());
        b0612.setPatientId(b032.getPatientId());
        b0612.setInpatientNo(b032.getInpatientNo());
        b0612.setHospitalCode(b032.getHospitalCode());
        b0612.setHospitalName(b032.getHospitalName());
        b0612.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充会诊记录特有信息
        b0612.setConsApplyTime(dto.getConsApplyTime());
        b0612.setConsTime(dto.getConsTime());
        b0612.setDiagnosisConsultationCorrected(dto.getDiagnosisConsultationCorrected());
        b0612.setAuxiliaryExam(dto.getAuxiliaryExam());
        b0612.setRecordAbstract(dto.getRecordAbstract());
        b0612.setTreatProDescription(dto.getTreatProDescription());
        b0612.setConsReason(dto.getConsReason());
        b0612.setDeptInvited(dto.getDeptInvited());
        b0612.setConsRecommendation(dto.getConsRecommendation());

        // 2.3 生成主键
        b0612.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b0612.setRecordDatetime(LocalDateTime.now());
        b0612.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b0612.setMedicalNoteDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b0612.setRecordStatus(1);
        b0612.setRecordText("");
        b0612.setRecordTitle("会诊记录");
        b0612.setYyRecordMd5(IdUtil.fastSimpleUUID());

        // 3. 保存到数据库
        this.save(b0612);

        // 4. 重新从数据库查询最新数据用于推送
        B0612 latestB0612 = this.getById(b0612.getRecordSn());
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0612.getHospitalCode());
        b061.setVisitSn(latestB0612.getVisitSn());
        b061.setRecordTitle(latestB0612.getRecordTitle());
        b061.setRecordType("会诊记录");
        b061.setRecordDatetime(latestB0612.getRecordDatetime());
        //以字段英文名与字段内容由冒号拼接，再由~!@#四个特殊英文字符，如下：
        // medical_note_date:字段内容~!@#cons_apply_time:字段内容~!@#cons_time:字段内容~!@#diagnosis_consultation_corrected:字段内容~!@#auxiliary_exam:字段内容~!@#record_abstract:字段内容~!@#treat_pro_description:字段内容~!@#cons_reason:字段内容~!@#dept_invited:字段内容~!@#cons_recommendation:字段内容
        String recordContent = buildRecordContent(latestB0612);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0612.getVisitSn(), B061.TABLE_NAME, b061);

        return b0612;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConsultationRecord(B0612UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getRecordSn())) {
            return false;
        }

        B0612 existingB0612 = this.getById(dto.getRecordSn());
        if (existingB0612 == null) {
            throw new IllegalArgumentException("未找到要更新的会诊记录");
        }

        B0612 b0612ToUpdate = converter.dtoToEntity(dto);
        b0612ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        boolean updated = this.updateById(b0612ToUpdate);

        // 推送更新后的数据到第三方系统
        if (updated) {
            // 重新从数据库查询最新数据用于推送
            B0612 latestB0612 = this.lambdaQuery().eq(B0612::getVisitSn, dto.getVisitSn()).one();
            B061 b061 = new B061();
            b061.setHospitalCode(latestB0612.getHospitalCode());
            b061.setVisitSn(latestB0612.getVisitSn());
            b061.setRecordTitle(latestB0612.getRecordTitle());
            b061.setRecordType("会诊记录");
            b061.setRecordDatetime(latestB0612.getRecordDatetime());

            // 按照注释中的格式拼接字段内容
            String recordContent = buildRecordContent(latestB0612);
            b061.setRecordContent(recordContent);

            integrationClientService.pushHospitalDataAsync(latestB0612.getVisitSn(), B061.TABLE_NAME, b061);
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B0612 b0612 = this.lambdaQuery().eq(B0612::getVisitSn, visitSn).one();
        if (b0612 == null) {
            throw new IllegalArgumentException("未找到要删除的会诊记录");
        }
        this.lambdaUpdate().eq(B0612::getVisitSn, visitSn).remove();
        B061 b061 = new B061();
        b061.setHospitalCode(b0612.getHospitalCode());
        b061.setVisitSn(b0612.getVisitSn());
        b061.setRecordTitle(b0612.getRecordTitle());
        b061.setRecordType("会诊记录");
        b061.setRecordDatetime(b0612.getRecordDatetime());

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B061.TABLE_NAME, b061);
        return true;
    }

    /**
     * 构建记录内容字符串
     * 按照指定格式拼接字段：字段英文名:字段内容~!@#字段英文名:字段内容...
     *
     * @param b0612 会诊记录实体
     * @return 拼接后的记录内容字符串
     */
    private String buildRecordContent(B0612 b0612) {
        StringBuilder recordContentBuilder = new StringBuilder();

        // 按照注释中的顺序拼接字段
        recordContentBuilder.append("medical_note_date:").append(b0612.getMedicalNoteDate() != null ? b0612.getMedicalNoteDate() : "").append("~!@#");
        recordContentBuilder.append("cons_apply_time:").append(b0612.getConsApplyTime() != null ? b0612.getConsApplyTime() : "").append("~!@#");
        recordContentBuilder.append("cons_time:").append(b0612.getConsTime() != null ? b0612.getConsTime() : "").append("~!@#");
        recordContentBuilder.append("diagnosis_consultation_corrected:").append(b0612.getDiagnosisConsultationCorrected() != null ? b0612.getDiagnosisConsultationCorrected() : "").append("~!@#");
        recordContentBuilder.append("auxiliary_exam:").append(b0612.getAuxiliaryExam() != null ? b0612.getAuxiliaryExam() : "").append("~!@#");
        recordContentBuilder.append("record_abstract:").append(b0612.getRecordAbstract() != null ? b0612.getRecordAbstract() : "").append("~!@#");
        recordContentBuilder.append("treat_pro_description:").append(b0612.getTreatProDescription() != null ? b0612.getTreatProDescription() : "").append("~!@#");
        recordContentBuilder.append("cons_reason:").append(b0612.getConsReason() != null ? b0612.getConsReason() : "").append("~!@#");
        recordContentBuilder.append("dept_invited:").append(b0612.getDeptInvited() != null ? b0612.getDeptInvited() : "").append("~!@#");
        recordContentBuilder.append("cons_recommendation:").append(b0612.getConsRecommendation() != null ? b0612.getConsRecommendation() : "");

        return recordContentBuilder.toString();
    }
}