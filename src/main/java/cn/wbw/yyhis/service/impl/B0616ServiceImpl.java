package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B0616Converter;
import cn.wbw.yyhis.mapper.B0616Mapper;
import cn.wbw.yyhis.model.dto.B0616UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.model.entity.B0616;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B0616Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class B0616ServiceImpl extends ServiceImpl<B0616Mapper, B0616> implements B0616Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B0616Converter converter = B0616Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B0616 addSurgeryRecord(B0616UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B0616 b0616 = new B0616();

        // 2.1 从 B032 填充公共信息
        b0616.setVisitSn(b032.getVisitSn());
        b0616.setPatientId(b032.getPatientId());
        b0616.setInpatientNo(b032.getInpatientNo());
        b0616.setHospitalCode(b032.getHospitalCode());
        b0616.setHospitalName(b032.getHospitalName());
        b0616.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充手术记录特有信息
        b0616.setPreOperativeDiagnosis(dto.getPreOperativeDiagnosis());
        b0616.setPostOperationDiagnosis(dto.getPostOperationDiagnosis());
        b0616.setSurgeryName(dto.getSurgeryName());
        b0616.setAnesthesiaMethod(dto.getAnesthesiaMethod());
        b0616.setSurgeryStarttime(dto.getSurgeryStarttime());
        b0616.setSurgeryEndtime(dto.getSurgeryEndtime());
        b0616.setSurgeryProcess(dto.getSurgeryProcess());


        // 2.3 生成主键
        b0616.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b0616.setRecordDatetime(LocalDateTime.now());
        b0616.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b0616.setMedicalNoteDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b0616.setRecordStatus(1);
        b0616.setRecordText("");
        b0616.setRecordTitle("手术记录");
        b0616.setYyRecordMd5(IdUtil.fastSimpleUUID());

        // 3. 保存到数据库
        this.save(b0616);

        // 4. 重新从数据库查询最新数据用于推送
        B0616 latestB0616 = this.getById(b0616.getRecordSn());
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0616.getHospitalCode());
        b061.setVisitSn(latestB0616.getVisitSn());
        b061.setRecordTitle(latestB0616.getRecordTitle());
        b061.setRecordType("手术记录");
        b061.setRecordDatetime(latestB0616.getRecordDatetime());
        // 以字段英文名与字段内容由冒号拼接，再由~!@#四个特殊英文字符，如下：
        // medical_note_date:字段内容~!@#pre_operative_diagnosis:字段内容~!@#post_operation_diagnosis:字段内容~!@#surgery_name:字段内容~!@#surgery_time:字段内容~!@#anesthesia_method:字段内容~!@#anesthesia_starttime:字段内容~!@#anesthesia_endtime:字段内容~!@#operation_treatment:字段内容~!@#surgery_process:字段内容~!@#bleeding_volum:字段内容~!@#volume_blood:字段内容~!@#surgery_starttime:字段内容~!@#surgery_endtime:字段内容~!@#surgery_doctor_code:字段内容~!@#surgery_doctor_name:字段内容~!@#anesthesia_doctor_code:字段内容~!@#anesthesia_doctor_name:字段内容
        String recordContent = buildRecordContent(latestB0616);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0616.getVisitSn(), B061.TABLE_NAME, b061);

        return b0616;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSurgeryRecord(B0616UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getRecordSn())) {
            return false;
        }

        B0616 existingB0616 = this.getById(dto.getRecordSn());
        if (existingB0616 == null) {
            throw new IllegalArgumentException("未找到要更新的手术记录");
        }

        B0616 b0616ToUpdate = converter.toEntity(dto);
        b0616ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        boolean updated = this.updateById(b0616ToUpdate);

        // 推送更新后的数据到第三方系统
        if (updated) {
            // 重新从数据库查询最新数据用于推送
            B0616 latestB0616 = this.getById(b0616ToUpdate.getRecordSn());
            B061 b061 = new B061();
            b061.setHospitalCode(latestB0616.getHospitalCode());
            b061.setVisitSn(latestB0616.getVisitSn());
            b061.setRecordTitle(latestB0616.getRecordTitle());
            b061.setRecordType("手术记录");
            b061.setRecordDatetime(latestB0616.getRecordDatetime());

            // 按照注释中的格式拼接字段内容
            String recordContent = buildRecordContent(latestB0616);
            b061.setRecordContent(recordContent);

            integrationClientService.pushHospitalDataAsync(latestB0616.getVisitSn(), B061.TABLE_NAME, b061);
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B0616 b0616 = this.lambdaQuery().eq(B0616::getVisitSn, visitSn).one();
        if (b0616 == null) {
            throw new IllegalArgumentException("未找到要删除的手术记录");
        }
        this.lambdaUpdate().eq(B0616::getVisitSn, visitSn).remove();
        B061 b061 = new B061();
        b061.setHospitalCode(b0616.getHospitalCode());
        b061.setVisitSn(b0616.getVisitSn());
        b061.setRecordTitle(b0616.getRecordTitle());
        b061.setRecordType("手术记录");
        b061.setRecordDatetime(b0616.getRecordDatetime());

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B061.TABLE_NAME, b061);
        return true;
    }

    /**
     * 构建记录内容字符串
     * 按照指定格式拼接字段：字段英文名:字段内容~!@#字段英文名:字段内容...
     *
     * @param b0616 手术记录实体
     * @return 拼接后的记录内容字符串
     */
    private String buildRecordContent(B0616 b0616) {
        StringBuilder recordContentBuilder = new StringBuilder();

        // 按照注释中的顺序拼接字段
        recordContentBuilder.append("medical_note_date:").append(b0616.getMedicalNoteDate() != null ? b0616.getMedicalNoteDate() : "").append("~!@#");
        recordContentBuilder.append("pre_operative_diagnosis:").append(b0616.getPreOperativeDiagnosis() != null ? b0616.getPreOperativeDiagnosis() : "").append("~!@#");
        recordContentBuilder.append("post_operation_diagnosis:").append(b0616.getPostOperationDiagnosis() != null ? b0616.getPostOperationDiagnosis() : "").append("~!@#");
        recordContentBuilder.append("surgery_name:").append(b0616.getSurgeryName() != null ? b0616.getSurgeryName() : "").append("~!@#");
        recordContentBuilder.append("surgery_time:").append(b0616.getSurgeryTime() != null ? b0616.getSurgeryTime() : "").append("~!@#");
        recordContentBuilder.append("anesthesia_method:").append(b0616.getAnesthesiaMethod() != null ? b0616.getAnesthesiaMethod() : "").append("~!@#");
        recordContentBuilder.append("anesthesia_starttime:").append(b0616.getAnesthesiaStarttime() != null ? b0616.getAnesthesiaStarttime() : "").append("~!@#");
        recordContentBuilder.append("anesthesia_endtime:").append(b0616.getAnesthesiaEndtime() != null ? b0616.getAnesthesiaEndtime() : "").append("~!@#");
        recordContentBuilder.append("operation_treatment:").append(b0616.getOperationTreatment() != null ? b0616.getOperationTreatment() : "").append("~!@#");
        recordContentBuilder.append("surgery_process:").append(b0616.getSurgeryProcess() != null ? b0616.getSurgeryProcess() : "").append("~!@#");
        recordContentBuilder.append("bleeding_volum:").append(b0616.getBleedingVolum() != null ? b0616.getBleedingVolum() : "").append("~!@#");
        recordContentBuilder.append("volume_blood:").append(b0616.getVolumeBlood() != null ? b0616.getVolumeBlood() : "").append("~!@#");
        recordContentBuilder.append("surgery_starttime:").append(b0616.getSurgeryStarttime() != null ? b0616.getSurgeryStarttime() : "").append("~!@#");
        recordContentBuilder.append("surgery_endtime:").append(b0616.getSurgeryEndtime() != null ? b0616.getSurgeryEndtime() : "").append("~!@#");
        recordContentBuilder.append("surgery_doctor_code:").append(b0616.getSurgeryDoctorCode() != null ? b0616.getSurgeryDoctorCode() : "").append("~!@#");
        recordContentBuilder.append("surgery_doctor_name:").append(b0616.getSurgeryDoctorName() != null ? b0616.getSurgeryDoctorName() : "").append("~!@#");
        recordContentBuilder.append("anesthesia_doctor_code:").append(b0616.getAnesthesiaDoctorCode() != null ? b0616.getAnesthesiaDoctorCode() : "").append("~!@#");
        recordContentBuilder.append("anesthesia_doctor_name:").append(b0616.getAnesthesiaDoctorName() != null ? b0616.getAnesthesiaDoctorName() : "");

        return recordContentBuilder.toString();
    }
}