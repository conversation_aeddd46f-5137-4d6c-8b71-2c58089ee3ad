package cn.wbw.yyhis.service.impl;

import cn.wbw.yyhis.converter.B161Converter;
import cn.wbw.yyhis.model.dto.B161DTO;
import cn.wbw.yyhis.model.entity.B161;
import cn.wbw.yyhis.mapper.B161Mapper;
import cn.wbw.yyhis.service.B161Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 常规检查记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Service
public class B161ServiceImpl extends ServiceImpl<B161Mapper, B161> implements B161Service {

    @Autowired
    private B161Converter b161Converter;
    @Autowired
    private IntegrationClientService integrationClientService;

    @Override
    public Boolean updateDto(B161DTO b161DTO) {
        // 我们只更新DTO中提供的字段，所以先根据ID获取现有实体
        B161 existingB161 = this.getById(b161DTO.getReportNo());
        if (existingB161 == null) {
            throw new IllegalArgumentException("未找到要更新的常规检查记录");
        }

        // 使用 converter 将DTO中的非空值更新到实体中
        B161 b161 = b161Converter.dtoToEntity(b161DTO);
        b161.setRecordUpdateDatetime(LocalDateTime.now());
        this.updateById(b161);
        integrationClientService.pushHospitalDataAsync(existingB161.getVisitSn(), B161.TABLE_NAME, b161);
        return true;
    }

    @Override
    public Boolean deleteByReportNo(String id) {
        B161 one = this.getById(id);
        if (one == null) {
            return true;
        }
        this.removeById(id);
        integrationClientService.deleteHospitalDataByCompositeKey(one.getVisitSn(), B161.TABLE_NAME, one);
        return true;
    }

}