package cn.wbw.yyhis.model.entity;

import cn.wbw.yyhis.annotation.CompositeKey;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <p>
 * 分子病理检测记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@TableName("b16_3")
public class B163 implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "b16_3";

    /**
     * 年龄(岁)
     */
    @TableField("age")
    private String age;

    /**
     * 氨基酸改变
     */
    @TableField("amino_acid_alt")
    private String aminoAcidAlt;

    /**
     * 申请时间
     */
    @TableField("apply_datetime")
    private String applyDatetime;

    /**
     * 申请单号
     */
    @TableField("apply_no")
    private String applyNo;

    /**
     * 出生日期
     */
    @TableField("date_of_birth")
    private String dateOfBirth;

    /**
     * 检测时间
     */
    @TableField("exam_datetime")
    private String examDatetime;

    /**
     * 检查医生代码
     */
    @TableField("exam_doc_code")
    private String examDocCode;

    /**
     * 检查医生姓名
     */
    @TableField("exam_doc_name")
    private String examDocName;

    /**
     * 扩展字段1
     */
    @TableField("extend_data1")
    private String extendData1;

    /**
     * 扩展字段2
     */
    @TableField("extend_data2")
    private String extendData2;

    /**
     * 来源表
     */
    @TableField("from_table")
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    /**
     * 性别
     */
    @TableField("gender")
    private String gender;

    /**
     * 测序深度
     */
    @TableField("gene_sequencing_depth")
    private String geneSequencingDepth;

    /**
     * 基因型
     */
    @TableField("geno_type")
    private String genoType;

    /**
     * 组织机构代码
     */
    @TableField("hospital_code")
    @CompositeKey(order = 1, name = "hospital_code")
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    @TableField("hospital_name")
    private String hospitalName;

    /**
     * 住院次数
     */
    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    /**
     * 住院号
     */
    @TableField("inpatient_no")
    private String inpatientNo;

    /**
     * 微生物检验流水号
     */
    @TableField("lab_sn")
    private String labSn;

    /**
     * 病案号
     */
    @TableField("medical_record_no")
    private String medicalRecordNo;

    /**
     * 分子病理号
     */
    @TableField("mol_patho_no")
    private String molPathoNo;

    /**
     * 丰度
     */
    @TableField("mut_abundance")
    private String mutAbundance;

    /**
     * 变异解读
     */
    @TableField("mut_interpretation")
    private String mutInterpretation;

    /**
     * 患者姓名
     */
    @TableField("name")
    private String name;

    /**
     * 核苷酸改变
     */
    @TableField("nucle_alt")
    private String nucleAlt;

    /**
     * 申请医嘱流水号
     */
    @TableField("order_sn")
    private String orderSn;

    /**
     * 门诊号
     */
    @TableField("outpatient_no")
    private String outpatientNo;

    /**
     * 患者ID
     */
    @TableField("patient_id")
    private String patientId;

    /**
     * 患者原始ID
     */
    @TableField("patient_id_old")
    private String patientIdOld;

    /**
     * 记录创建时间
     */
    @TableField("record_datetime")
    private String recordDatetime;

    /**
     * 记录状态
     */
    @TableField("record_status")
    private Integer recordStatus;

    /**
     * 记录更新时间
     */
    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    /**
     * 报告时间
     */
    @TableField("report_datetime")
    private String reportDatetime;

    /**
     * 报告科室代码
     */
    @TableField("report_dept_code")
    private String reportDeptCode;

    /**
     * 报告科室名称
     */
    @TableField("report_dept_name")
    private String reportDeptName;

    /**
     * 报告医生代码
     */
    @TableField("report_doc_code")
    private String reportDocCode;

    /**
     * 报告医生姓名
     */
    @TableField("report_doc_name")
    private String reportDocName;

    /**
     * 报告单号
     */
    @TableId(value = "report_no", type = IdType.INPUT)
    @CompositeKey(order = 4, name = "report_no")
    private String reportNo;

    /**
     * 标本编号
     */
    @TableField("sample_no")
    private String sampleNo;

    /**
     * 标本类型
     */
    @TableField("sample_type")
    private String sampleType;

    /**
     * 取材部位
     */
    @TableField("sampling_site")
    private String samplingSite;

    /**
     * 检测内容
     */
    @TableField("test_content")
    private String testContent;

    /**
     * 外显子
     */
    @TableField("test_exon")
    @CompositeKey(order = 5, name = "test_exon")
    private String testExon;

    /**
     * 检测基因
     */
    @TableField("test_gene")
    private String testGene;

    /**
     * 检测项目
     */
    @TableField("test_item")
    private String testItem;

    /**
     * 位点
     */
    @TableField("test_locus")
    @CompositeKey(order = 6, name = "test_locus")
    private String testLocus;

    /**
     * 检测方法
     */
    @TableField("test_method")
    private String testMethod;

    /**
     * 检测平台
     */
    @TableField("test_platform")
    private String testPlatform;

    /**
     * 检测结果
     */
    @TableField("test_result")
    @CompositeKey(order = 3, name = "test_result")
    private String testResult;

    /**
     * 肿瘤突变负荷
     */
    @TableField("tmb")
    private String tmb;

    /**
     * 转录本
     */
    @TableField("transcript")
    private String transcript;

    /**
     * 变异类型
     */
    @TableField("variation_type")
    private String variationType;

    /**
     * 审核医师代码
     */
    @TableField("verify_doc_code")
    private String verifyDocCode;

    /**
     * 审核医生姓名
     */
    @TableField("verify_doc_name")
    private String verifyDocName;

    /**
     * 就诊卡号
     */
    @TableField("visit_card_no")
    private String visitCardNo;

    /**
     * 单次就诊唯一标识号
     */
    @TableField("visit_sn")
    @CompositeKey(order = 2, name = "visit_sn")
    private String visitSn;

    /**
     * 就诊次数
     */
    @TableField("visit_times")
    private String visitTimes;

    /**
     * 就诊类型
     */
    @TableField("visit_type")
    private String visitType;

    /**
     * 单次就诊唯一标识号（原始）
     */
    @TableField("visit_sn_old")
    @CompositeKey(order = 7,name = "visit_sn_old")
    private String visitSnOld;

    /**
     * 数据采集时间
     */
    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    @TableField(value = "yy_record_id", fill = com.baomidou.mybatisplus.annotation.FieldFill.INSERT_UPDATE)
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    @TableField("yy_record_md5")
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    @TableField("yy_batch_time")
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    @TableField("branch_code")
    private String branchCode;

    /**
     * 分院名称
     */
    @TableField("branch_name")
    private String branchName;

    /**
     * 分区用业务日期
     */
    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
} 