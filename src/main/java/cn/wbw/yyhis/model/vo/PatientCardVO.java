package cn.wbw.yyhis.model.vo;

import lombok.Data;

import java.time.LocalDate;

@Data
public class PatientCardVO {

    /**
     * 病历号
     */
    private String medicalRecordNo;

    /**
     * 住院次数
     */
    private Integer hospitalizationCount;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 床号
     */
    private String bedNo;

    /**
     * 床位医生
     */
    private String attendingPhysician;

    /**
     * 入院诊断
     */
    private String admissionDiagnosis;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 入院时间
     */
    private LocalDate admissionTime;

    /**
     * 出院时间
     */
    private LocalDate dischargeTime;

    /**
     * 住院天数
     */
    private Integer hospitalizationDays;

    /**
     * 性别
     */
    private String gender;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 备注
     */
    private String remark;
}